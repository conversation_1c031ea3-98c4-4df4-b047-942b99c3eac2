#!/usr/bin/env python3
"""
Phase 17C: Simplified Prompt Test

Tests ComfyUI generation with clean, natural language prompts without tags.
"""

import sys
import cv2
import numpy as np
from pathlib import Path
import datetime
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.emotion_matcher import EmotionMatcher
from core.pose_matcher import PoseMatcher
from scripts.generate_panel import ComfyUIGenerator


def log_prompt(prompt: str, llm_provider: str):
    """Log generated prompt to phase17c_prompts.log"""
    log_file = Path("logs/phase17c_prompts.log")
    log_file.parent.mkdir(exist_ok=True)
    
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] LLM: {llm_provider}\n")
        f.write(f"Prompt: {prompt}\n")
        f.write("---\n")


def save_debug_input(image_path: str, panel_num: int, test_type: str):
    """Save validation input for inspection"""
    debug_dir = Path("debug/phase17c_inputs")
    debug_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy image to debug directory
    if Path(image_path).exists():
        import shutil
        debug_path = debug_dir / f"simplified_{panel_num:02d}_{test_type}.png"
        shutil.copy2(image_path, debug_path)
        print(f"Saved debug input: {debug_path}")


def create_grid_image(image_paths: list, output_path: str):
    """Create a 2x3 grid of the 5 simplified images"""
    if not image_paths:
        return
    
    # Load images
    images = []
    for path in image_paths:
        if Path(path).exists():
            img = cv2.imread(str(path))
            if img is not None:
                # Resize to standard size (256x384 for grid)
                img = cv2.resize(img, (256, 384))
                images.append(img)
    
    if len(images) < 5:
        print(f"Warning: Only {len(images)} valid images for grid")
        return
    
    # Create 2x3 grid (5 images + 1 empty slot)
    row1 = np.hstack([images[0], images[1], images[2]])
    row2 = np.hstack([images[3], images[4], np.zeros_like(images[0])])
    grid = np.vstack([row1, row2])
    
    # Save grid
    cv2.imwrite(output_path, grid)
    print(f"Created grid image: {output_path}")


def run_simplified_test():
    """Run the simplified prompt test"""
    print("🧪 Starting Phase 17C: Simplified Prompt Test")
    
    # Simplified prompt (no tags, natural language only)
    simplified_prompt = "a girl jumping, smiling happily, manga style, white background"
    
    print(f"📝 Testing simplified prompt: {simplified_prompt}")
    print("🚫 Removed: [emotion: happy], [pose: jumping] tags")
    
    # Initialize matchers and generator
    emotion_matcher = EmotionMatcher()
    pose_matcher = PoseMatcher()
    generator = ComfyUIGenerator()
    
    # Test results
    results = []
    image_paths = []
    
    # Create output directory
    output_dir = Path("tests/gold_panels_v2")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate 5 simplified panels
    for i in range(1, 6):
        print(f"\n📸 Generating simplified panel {i}/5")
        
        # Log the prompt
        log_prompt(simplified_prompt, "simplified_natural")
        
        # Generate panel
        panel_filename = f"simplified_{i:02d}.png"
        panel_path = output_dir / panel_filename
        
        try:
            # Generate using ComfyUI
            success = generator.generate_panel(
                prompt=simplified_prompt,
                output_path=str(panel_path)
            )
            
            if not success:
                print(f"❌ Failed to generate {panel_filename}")
                results.append({
                    "filename": panel_filename,
                    "prompt_type": "simplified_natural",
                    "detected_face_count": 0,
                    "detected_pose_landmarks": 0,
                    "emotion_confidence": 0.0,
                    "pose_confidence": 0.0,
                    "image_quality": "invalid_image_quality",
                    "error": "Generation failed"
                })
                continue
            
            # Save debug input
            save_debug_input(str(panel_path), i, "simplified")
            image_paths.append(str(panel_path))
            
            # Test emotion detection
            print(f"🔍 Testing emotion detection for {panel_filename}")
            detected_emotion, emotion_confidence = emotion_matcher.detect_visual_emotion(str(panel_path))
            
            # Test pose detection  
            print(f"🔍 Testing pose detection for {panel_filename}")
            detected_pose, pose_confidence = pose_matcher.detect_visual_pose(str(panel_path))
            
            # Determine image quality
            is_valid = (detected_emotion != "invalid_image_quality" and 
                       detected_pose != "invalid_image_quality")
            
            # Count faces/landmarks (simplified for test)
            face_count = 1 if detected_emotion != "invalid_image_quality" else 0
            landmark_count = 33 if detected_pose != "invalid_image_quality" else 0
            
            result = {
                "filename": panel_filename,
                "prompt_type": "simplified_natural",
                "detected_face_count": face_count,
                "detected_pose_landmarks": landmark_count,
                "emotion_confidence": emotion_confidence,
                "pose_confidence": pose_confidence,
                "image_quality": "valid" if is_valid else "invalid_image_quality"
            }
            
            results.append(result)
            
            print(f"   Face count: {face_count}")
            print(f"   Pose landmarks: {landmark_count}")
            print(f"   Emotion: {detected_emotion} (conf: {emotion_confidence:.2f})")
            print(f"   Pose: {detected_pose} (conf: {pose_confidence:.2f})")
            print(f"   Quality: {result['image_quality']}")
            
        except Exception as e:
            print(f"❌ Error processing {panel_filename}: {e}")
            results.append({
                "filename": panel_filename,
                "prompt_type": "simplified_natural",
                "detected_face_count": 0,
                "detected_pose_landmarks": 0,
                "emotion_confidence": 0.0,
                "pose_confidence": 0.0,
                "image_quality": "invalid_image_quality",
                "error": str(e)
            })
    
    # Create grid image
    if image_paths:
        create_grid_image(image_paths, "debug/phase17c_simplified_grid.png")
    
    # Analyze results
    valid_count = sum(1 for r in results if r["image_quality"] == "valid")
    total_count = len(results)
    success_rate = valid_count / total_count if total_count > 0 else 0
    
    print(f"\n📊 Simplified Test Results:")
    print(f"   Valid images: {valid_count}/{total_count} ({success_rate:.1%})")
    
    # Generate report
    generate_simplified_report(results, simplified_prompt, success_rate)
    
    # Determine if simplified prompts work
    if valid_count >= 3:  # ≥3/5 images valid
        print(f"\n✅ SIMPLIFIED PROMPTS WORK! Root cause: Square bracket tags")
        print(f"   Recommendation: Remove [emotion: X], [pose: Y] tags from prompts")
        return True
    else:
        print(f"\n❌ SIMPLIFIED PROMPTS ALSO FAIL. Deeper ComfyUI issue.")
        print(f"   Recommendation: Check SD model, ControlNet, or workflow configuration")
        return False


def generate_simplified_report(results: list, prompt: str, success_rate: float):
    """Generate the Phase 17C simplified test report"""
    
    report_path = Path("reports/phase17c_simplified_test.md")
    report_path.parent.mkdir(exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# Phase 17C: Simplified Prompt Test Report\n\n")
        f.write(f"**Date**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**Test Type**: Natural language prompt without tags\n")
        f.write(f"**Success Rate**: {success_rate:.1%}\n\n")
        
        f.write("## Test Configuration\n\n")
        f.write(f"**Simplified Prompt**: `{prompt}`\n")
        f.write("**Removed Elements**: `[emotion: happy], [pose: jumping]` tags\n")
        f.write("**Test Images**: 5 panels\n")
        f.write("**Detection Method**: Haar cascade + MediaPipe fallback\n\n")
        
        f.write("## Results Table\n\n")
        f.write("| Filename | Face Count | Pose Landmarks | Emotion Conf | Pose Conf | Image Quality |\n")
        f.write("|----------|------------|----------------|--------------|-----------|---------------|\n")
        
        for result in results:
            f.write(f"| {result['filename']} | {result['detected_face_count']} | "
                   f"{result['detected_pose_landmarks']} | {result['emotion_confidence']:.2f} | "
                   f"{result['pose_confidence']:.2f} | {result['image_quality']} |\n")
        
        f.write(f"\n## Analysis\n\n")
        
        valid_count = sum(1 for r in results if r["image_quality"] == "valid")
        
        if valid_count >= 3:
            f.write("### ✅ ROOT CAUSE IDENTIFIED\n\n")
            f.write("**Issue**: Square bracket tags `[emotion: X], [pose: Y]` are confusing the SD model.\n\n")
            f.write("**Solution**: Remove emotion/pose tags from ComfyUI prompts.\n\n")
            f.write("**Recommendation**: Update prompt generation to use natural language only.\n\n")
        else:
            f.write("### ❌ DEEPER COMFYUI ISSUE\n\n")
            f.write("**Issue**: Even simplified prompts fail to generate detectable faces.\n\n")
            f.write("**Possible Causes**:\n")
            f.write("- SD model not trained for human faces\n")
            f.write("- ControlNet configuration issues\n")
            f.write("- Workflow node problems\n")
            f.write("- Resolution/quality settings\n\n")
            f.write("**Recommendation**: Check ComfyUI workflow and model configuration.\n\n")
        
        f.write("## Generated Artifacts\n\n")
        f.write("- `tests/gold_panels_v2/` - Simplified test images\n")
        f.write("- `debug/phase17c_simplified_grid.png` - Combined grid view\n")
        f.write("- `logs/phase17c_prompts.log` - Prompt logs\n")
        f.write("- `debug/phase17c_inputs/` - Debug validation inputs\n")
    
    print(f"📄 Report saved to: {report_path}")


if __name__ == "__main__":
    success = run_simplified_test()
    sys.exit(0 if success else 1)
