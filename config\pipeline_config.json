{"generation_method": "txt2img", "controlnet_type": "depth", "adapter_type": "sketch", "model_paths": {"controlnet": {"canny": "models/controlnet/control_sd15_canny.pth", "openpose": "models/controlnet/control_sd15_openpose.pth", "depth": "models/controlnet/control_sd15_depth.pth"}, "adapter": {"canny": "models/t2i_adapter/t2iadapter_canny_sd15v2.pth", "depth": "models/t2i_adapter/t2iadapter_depth_sd15v2.pth", "sketch": "models/t2i_adapter/t2iadapter_sketch_sd15v2.pth"}}, "reference_paths": {"controlnet": {"canny": "assets/references/controlnet/scene_canny.png", "openpose": "assets/references/controlnet/scene_pose.png", "depth": "assets/references/controlnet/scene_depth.png"}, "adapter": {"canny": "assets/references/adapter/scene_canny.png", "depth": "assets/references/adapter/scene_depth.png", "sketch": "assets/references/adapter/scene_sketch.png"}}, "generation_settings": {"controlnet_strength": 0.8, "adapter_strength": 0.7, "fallback_to_txt2img": true, "auto_detect_references": true}, "workflow_templates": {"txt2img": "assets/workflows/manga_graph.json", "controlnet": "assets/workflows/controlnet_workflow.json", "adapter": "assets/workflows/adapter_workflow.json"}}