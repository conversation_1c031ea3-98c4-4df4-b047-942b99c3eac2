{"last_node_id": 15, "last_link_id": 20, "nodes": [{"id": 1, "type": "CheckpointLoaderSimple", "pos": [100, 100], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2, 3], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [4], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["C:\\Users\\<USER>\\MangaGen\\models\\checkpoints\\waifu-diffusion-v1.4.ckpt"]}, {"id": 2, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [450, 100], "size": {"0": 315, "1": 126}, "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "clip", "type": "CLIP", "link": 2}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [5], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [6], "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["C:\\Users\\<USER>\\MangaGen\\models\\loras\\anime_lineart_lora.safetensors", 0.8, 0.8]}, {"id": 3, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [800, 100], "size": {"0": 315, "1": 126}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5}, {"name": "clip", "type": "CLIP", "link": 6}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [7], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [8, 9], "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["C:\\Users\\<USER>\\MangaGen\\models\\loras\\manga_shading_lora.safetensors", 0.5, 0.5]}, {"id": 4, "type": "ControlNetLoader", "pos": [100, 300], "size": {"0": 315, "1": 58}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["C:\\Users\\<USER>\\MangaGen\\models\\controlnet\\control_sd15_openpose.pth"]}, {"id": 5, "type": "LoadImage", "pos": [100, 400], "size": {"0": 315, "1": 314}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pose_map.png", "image"]}, {"id": 6, "type": "ControlNetApply", "pos": [450, 300], "size": {"0": 315, "1": 98}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 12}, {"name": "control_net", "type": "CONTROL_NET", "link": 10}, {"name": "image", "type": "IMAGE", "link": 11}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetApply"}, "widgets_values": [1.0]}, {"id": 7, "type": "LoadImage", "pos": [100, 750], "size": {"0": 315, "1": 314}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [14], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["C:\\Users\\<USER>\\MangaGen\\assets\\references\\your_face.png", "image"]}, {"id": 8, "type": "IPAdapterModelLoader", "pos": [450, 750], "size": {"0": 315, "1": 58}, "flags": {}, "order": 7, "mode": 0, "outputs": [{"name": "IPADAPTER", "type": "IPADAPTER", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterModelLoader"}, "widgets_values": ["C:\\Users\\<USER>\\MangaGen\\models\\ipadapter\\ipadapter-face-v1.pth"]}, {"id": 9, "type": "IPAdapterApply", "pos": [800, 750], "size": {"0": 315, "1": 158}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "ipadapter", "type": "IPADAPTER", "link": 15}, {"name": "clip_vision", "type": "CLIP_VISION", "link": null}, {"name": "image", "type": "IMAGE", "link": 14}, {"name": "model", "type": "MODEL", "link": 7}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [16], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterApply"}, "widgets_values": [1.0, 0.0, "original", 0.0, 1.0, false]}, {"id": 10, "type": "CLIPTextEncode", "pos": [1150, 100], "size": {"0": 400, "1": 200}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 8}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["PROMPT_PLACEHOLDER"]}, {"id": 11, "type": "CLIPTextEncode", "pos": [1150, 350], "size": {"0": 400, "1": 200}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 9}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [17], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, artist name, bad feet, bad proportions, extra limbs, disfigured, out of frame, ugly, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, out of frame, ugly, extra limbs, bad anatomy, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, mutated hands, fused fingers, too many fingers, long neck"]}, {"id": 12, "type": "K<PERSON><PERSON><PERSON>", "pos": [1600, 100], "size": {"0": 315, "1": 262}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 16}, {"name": "positive", "type": "CONDITIONING", "link": 13}, {"name": "negative", "type": "CONDITIONING", "link": 17}, {"name": "latent_image", "type": "LATENT", "link": 18}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [19], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [42, "randomize", 25, 7.0, "dpmpp_2m_karras", "karras", 1.0]}, {"id": 13, "type": "EmptyLatentImage", "pos": [1600, 400], "size": {"0": 315, "1": 106}, "flags": {}, "order": 12, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [18], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 768, 1]}, {"id": 14, "type": "VAEDecode", "pos": [1950, 100], "size": {"0": 210, "1": 46}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 19}, {"name": "vae", "type": "VAE", "link": 4}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [20], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 15, "type": "SaveImage", "pos": [2200, 100], "size": {"0": 315, "1": 270}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 20}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["C:\\Users\\<USER>\\MangaGen\\outputs\\bwmanga"]}]}