#!/usr/bin/env python3
"""
Golden Test for Phase 17B Hotfix

Tests the pipeline with a hardcoded "golden" prompt that should yield clear faces and poses.
"""

import sys
import cv2
import numpy as np
from pathlib import Path
import datetime
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.emotion_matcher import EmotionMatcher
from core.pose_matcher import PoseMatcher
from scripts.generate_panel import ComfyUIGenerator


def log_prompt(prompt: str, llm_provider: str):
    """Log generated prompt to phase17b_prompts.log"""
    log_file = Path("logs/phase17b_prompts.log")
    log_file.parent.mkdir(exist_ok=True)
    
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] LLM: {llm_provider}\n")
        f.write(f"Prompt: {prompt}\n")
        f.write("---\n")


def save_debug_input(image_path: str, panel_num: int, llm_name: str):
    """Save validation input for inspection"""
    debug_dir = Path("debug/phase17b_inputs")
    debug_dir.mkdir(parents=True, exist_ok=True)
    
    # Copy image to debug directory
    if Path(image_path).exists():
        import shutil
        debug_path = debug_dir / f"input_{panel_num:02d}_{llm_name}.png"
        shutil.copy2(image_path, debug_path)
        print(f"Saved debug input: {debug_path}")


def create_grid_image(image_paths: list, output_path: str):
    """Create a 2x3 grid of the 5 golden images"""
    if not image_paths:
        return
    
    # Load images
    images = []
    for path in image_paths:
        if Path(path).exists():
            img = cv2.imread(str(path))
            if img is not None:
                # Resize to standard size (256x384 for grid)
                img = cv2.resize(img, (256, 384))
                images.append(img)
    
    if len(images) < 5:
        print(f"Warning: Only {len(images)} valid images for grid")
        return
    
    # Create 2x3 grid (5 images + 1 empty slot)
    row1 = np.hstack([images[0], images[1], images[2]])
    row2 = np.hstack([images[3], images[4], np.zeros_like(images[0])])
    grid = np.vstack([row1, row2])
    
    # Save grid
    cv2.imwrite(output_path, grid)
    print(f"Created grid image: {output_path}")


def run_golden_test():
    """Run the golden test with hardcoded prompt"""
    print("🏆 Starting Golden Test for Phase 17B")

    # Golden prompt (hardcoded as specified)
    golden_prompt = "girl jumping in mid-air, big smile, clean white background, manga style, [emotion: happy], [pose: jumping]"

    # Initialize matchers and generator
    emotion_matcher = EmotionMatcher()
    pose_matcher = PoseMatcher()
    generator = ComfyUIGenerator()

    # Test results
    results = []
    image_paths = []
    
    # Generate 5 golden panels
    for i in range(1, 6):
        print(f"\n📸 Generating golden panel {i}/5")
        
        # Log the prompt
        log_prompt(golden_prompt, "hardcoded_golden")
        
        # Generate panel
        panel_filename = f"gold_{i:02d}.png"
        panel_path = Path("tests/gold_panels") / panel_filename
        panel_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # Generate using ComfyUI
            success = generator.generate_panel(
                prompt=golden_prompt,
                output_path=str(panel_path)
            )
            
            if not success:
                print(f"❌ Failed to generate {panel_filename}")
                results.append({
                    "filename": panel_filename,
                    "llm_provider": "hardcoded_golden",
                    "detected_face_count": 0,
                    "detected_pose_landmarks": 0,
                    "emotion_confidence": 0.0,
                    "pose_confidence": 0.0,
                    "image_quality": "invalid_image_quality",
                    "error": "Generation failed"
                })
                continue
            
            # Save debug input
            save_debug_input(str(panel_path), i, "golden")
            image_paths.append(str(panel_path))
            
            # Test emotion detection
            print(f"🔍 Testing emotion detection for {panel_filename}")
            detected_emotion, emotion_confidence = emotion_matcher.detect_visual_emotion(str(panel_path))
            
            # Test pose detection  
            print(f"🔍 Testing pose detection for {panel_filename}")
            detected_pose, pose_confidence = pose_matcher.detect_visual_pose(str(panel_path))
            
            # Determine image quality
            is_valid = (detected_emotion != "invalid_image_quality" and 
                       detected_pose != "invalid_image_quality")
            
            # Count faces/landmarks (simplified for golden test)
            face_count = 1 if detected_emotion != "invalid_image_quality" else 0
            landmark_count = 33 if detected_pose != "invalid_image_quality" else 0
            
            result = {
                "filename": panel_filename,
                "llm_provider": "hardcoded_golden",
                "detected_face_count": face_count,
                "detected_pose_landmarks": landmark_count,
                "emotion_confidence": emotion_confidence,
                "pose_confidence": pose_confidence,
                "image_quality": "valid" if is_valid else "invalid_image_quality"
            }
            
            results.append(result)
            
            print(f"   Face count: {face_count}")
            print(f"   Pose landmarks: {landmark_count}")
            print(f"   Emotion: {detected_emotion} (conf: {emotion_confidence:.2f})")
            print(f"   Pose: {detected_pose} (conf: {pose_confidence:.2f})")
            print(f"   Quality: {result['image_quality']}")
            
        except Exception as e:
            print(f"❌ Error processing {panel_filename}: {e}")
            results.append({
                "filename": panel_filename,
                "llm_provider": "hardcoded_golden",
                "detected_face_count": 0,
                "detected_pose_landmarks": 0,
                "emotion_confidence": 0.0,
                "pose_confidence": 0.0,
                "image_quality": "invalid_image_quality",
                "error": str(e)
            })
    
    # Create grid image
    if image_paths:
        create_grid_image(image_paths, "debug/phase17b_gold_inputs_grid.png")
    
    # Check if any golden images failed
    invalid_count = sum(1 for r in results if r["image_quality"] == "invalid_image_quality")
    
    if invalid_count > 0:
        print(f"\n❌ GOLDEN TEST FAILED: {invalid_count}/5 images invalid")
        
        # Write failure report
        report_path = "phase17b_hotfix_report.md"
        with open(report_path, 'w') as f:
            f.write("# Phase 17B Hotfix Report\n\n")
            f.write("## Golden Test Results: FAILED\n\n")
            f.write(f"**Failed Images**: {invalid_count}/5\n\n")
            f.write("### Failed Images:\n")
            for result in results:
                if result["image_quality"] == "invalid_image_quality":
                    f.write(f"- {result['filename']}: {result.get('error', 'Invalid image quality')}\n")
            f.write(f"\n**Golden Prompt**: `{golden_prompt}`\n\n")
            f.write("**Note**: ComfyUI pipeline or prompt_builder is producing unusable outputs—fix required.\n")
        
        print(f"📄 Failure report written to: {report_path}")
        return False
    else:
        print(f"\n✅ GOLDEN TEST PASSED: All 5 images valid")
        return True


if __name__ == "__main__":
    success = run_golden_test()
    sys.exit(0 if success else 1)
